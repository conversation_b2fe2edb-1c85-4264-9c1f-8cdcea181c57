import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    });

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ success: false, error: 'Admin access required' }, { status: 403 });
    }

    const { userId } = params;

    // Fetch user's binary match history
    const matchHistory = await prisma.binaryMatchHistory.findMany({
      where: {
        userId: userId
      },
      include: {
        user: {
          select: {
            email: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        matchDate: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: matchHistory
    });

  } catch (error) {
    console.error('Binary points user history fetch error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user binary match history' },
      { status: 500 }
    );
  }
}

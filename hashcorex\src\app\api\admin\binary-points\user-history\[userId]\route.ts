import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateRequest, isAdmin } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json({ success: false, error: 'Admin access required' }, { status: 403 });
    }

    const { userId } = params;

    // Fetch user's binary match history from transactions table
    const matchHistory = await prisma.transaction.findMany({
      where: {
        userId: userId,
        type: 'BINARY_BONUS',
        status: 'COMPLETED'
      },
      include: {
        user: {
          select: {
            email: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Transform data to match expected format
    const transformedHistory = await Promise.all(
      matchHistory.map(async (transaction) => {
        // Try to get additional details from system logs
        const logEntry = await prisma.systemLog.findFirst({
          where: {
            action: {
              in: ['BINARY_MATCHING_PROCESSED', 'MANUAL_BINARY_MATCHING_TRIGGERED'],
            },
            userId: userId,
            createdAt: {
              gte: new Date(transaction.createdAt.getTime() - 60000), // Within 1 minute
              lte: new Date(transaction.createdAt.getTime() + 60000),
            },
          },
          orderBy: { createdAt: 'desc' },
        });

        // Extract matching type from log or transaction description
        const isManual = transaction.description?.includes('Manual') ||
                        logEntry?.action === 'MANUAL_BINARY_MATCHING_TRIGGERED';

        return {
          id: transaction.id,
          userId: transaction.userId,
          user: transaction.user,
          matchedPoints: transaction.amount / 10, // Assuming $10 per point
          payout: transaction.amount,
          matchDate: transaction.createdAt,
          type: isManual ? 'MANUAL' : 'WEEKLY',
          description: transaction.description,
          // Note: We don't have before/after points in current schema
          // These would need to be added to track properly
          leftPointsBefore: 0,
          rightPointsBefore: 0,
          leftPointsAfter: 0,
          rightPointsAfter: 0,
        };
      })
    );

    return NextResponse.json({
      success: true,
      data: transformedHistory
    });

  } catch (error) {
    console.error('Binary points user history fetch error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user binary match history' },
      { status: 500 }
    );
  }
}

'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardContent, Button } from '@/components/ui';
import { Grid } from '@/components/layout';
import { 
  Users, Copy, Share2, TrendingUp, <PERSON>, RefreshCw, Eye, EyeOff,
  ZoomIn, ZoomOut, Maximize2
} from 'lucide-react';
import { formatCurrency, formatDateTime, copyToClipboard } from '@/lib/utils';
import { BinaryPointsInfoPanel } from './BinaryPointsInfoPanel';
import { hierarchy, tree } from 'd3-hierarchy';
import { select } from 'd3-selection';
import { zoom, zoomIdentity } from 'd3-zoom';
import 'd3-transition';

interface BinaryTreeNode {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: string;
    isActive: boolean;
  };
  sponsorInfo?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  directReferralCount: number;
  teamCounts: {
    left: number;
    right: number;
    total: number;
  };
  binaryPoints: {
    leftPoints: number;
    rightPoints: number;
    matchedPoints: number;
  };
  hasLeftChild?: boolean;
  hasRightChild?: boolean;
  leftChild?: BinaryTreeNode;
  rightChild?: BinaryTreeNode;
}

interface BinaryTreeData {
  treeStructure: BinaryTreeNode;
  statistics: {
    totalDirectReferrals: number;
    leftReferrals: number;
    rightReferrals: number;
    totalCommissions: number;
    binaryPoints: {
      leftPoints: number;
      rightPoints: number;
      matchedPoints: number;
    };
  };
  referralLinks: {
    left: string;
    right: string;
    general: string;
  };
}

interface D3TreeNode extends d3.HierarchyNode<BinaryTreeNode> {
  _children?: D3TreeNode[];
  x0?: number;
  y0?: number;
}

// Import d3 namespace for types
import * as d3 from 'd3-hierarchy';

export const D3BinaryTree: React.FC = () => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [treeData, setTreeData] = useState<BinaryTreeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [showInactive, setShowInactive] = useState(true);
  
  // D3 tree layout configuration
  const margin = { top: 50, right: 120, bottom: 50, left: 120 };
  const width = 1200 - margin.left - margin.right;
  const height = 800 - margin.bottom - margin.top;
  const nodeWidth = 200;
  const nodeHeight = 120;

  const fetchTreeData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/referrals/tree?depth=20&enhanced=true&expanded=${Array.from(expandedNodes).join(',')}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTreeData(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch binary tree data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Convert binary tree structure to D3 hierarchy format with expand/collapse support
  const convertToD3Hierarchy = useCallback((node: BinaryTreeNode): d3.HierarchyNode<BinaryTreeNode> => {
    const hierarchyNode = hierarchy(node, (d) => {
      // Check if this node should show its children
      const isExpanded = expandedNodes.has(d.user.id);

      // Root node (first level) is always expanded, others depend on expanded state
      const shouldShowChildren = d === node || isExpanded;

      if (!shouldShowChildren) {
        return null; // Don't show children if node is collapsed
      }

      const nodeChildren: BinaryTreeNode[] = [];

      // Add left child if it exists and should be visible
      if (d.leftChild && (showInactive || d.leftChild.user.isActive)) {
        nodeChildren.push(d.leftChild);
      }

      // Add right child if it exists and should be visible
      if (d.rightChild && (showInactive || d.rightChild.user.isActive)) {
        nodeChildren.push(d.rightChild);
      }
      return nodeChildren.length > 0 ? nodeChildren : null;
    });

    return hierarchyNode;
  }, [showInactive, expandedNodes]);

  // Initialize and render the D3 tree
  const renderTree = useCallback(() => {
    if (!treeData || !svgRef.current) return;

    try {
      const svg = select(svgRef.current);
      svg.selectAll("*").remove(); // Clear previous render

      // Create main group for zoom/pan
      const container = svg
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom);

      const g = container
        .append("g")
        .attr("class", "tree-container")
        .attr("transform", `translate(${margin.left},${margin.top})`);

      // Setup zoom behavior
      const zoomBehavior = zoom<SVGSVGElement, unknown>()
        .scaleExtent([0.1, 3])
        .on("zoom", (event) => {
          g.attr("transform", event.transform);
        });

      svg.call(zoomBehavior);

      // Create tree layout
      const treeLayout = tree<BinaryTreeNode>()
        .size([width, height])
        .nodeSize([nodeWidth + 50, nodeHeight + 80]) // Add spacing between nodes
        .separation((a, b) => {
          // Increase separation for better spacing
          return a.parent === b.parent ? 2 : 3;
        });

      // Convert data to hierarchy and apply layout
      const root = convertToD3Hierarchy(treeData.treeStructure);
      const treeRoot = treeLayout(root) as D3TreeNode;

      // Center the tree
      const nodes = treeRoot.descendants();
      const links = treeRoot.links();

      // Adjust positions to center the tree
      let minX = Infinity;
      let maxX = -Infinity;
      nodes.forEach(d => {
        if (d.x < minX) minX = d.x;
        if (d.x > maxX) maxX = d.x;
      });
      const centerOffset = (width / 2) - ((minX + maxX) / 2);
      nodes.forEach(d => {
        d.x += centerOffset;
      });

    // Render links (connections between nodes)
    const linkGroup = g.append("g").attr("class", "links");
    
    linkGroup.selectAll(".link")
      .data(links)
      .enter()
      .append("path")
      .attr("class", "link")
      .attr("d", (d: any) => {
        const source = d.source;
        const target = d.target;
        
        // Create smooth curved path
        return `M${source.x},${source.y + nodeHeight/2}
                C${source.x},${(source.y + target.y) / 2}
                 ${target.x},${(source.y + target.y) / 2}
                 ${target.x},${target.y - nodeHeight/2}`;
      })
      .style("fill", "none")
      .style("stroke", "#94a3b8")
      .style("stroke-width", "2px")
      .style("stroke-opacity", 0.6);

    // Render nodes
    const nodeGroup = g.append("g").attr("class", "nodes");
    
    const nodeEnter = nodeGroup.selectAll(".node")
      .data(nodes)
      .enter()
      .append("g")
      .attr("class", "node")
      .attr("transform", (d: any) => `translate(${d.x - nodeWidth/2},${d.y - nodeHeight/2})`)
      .style("cursor", "pointer");

    // Add node background (card)
    nodeEnter
      .append("rect")
      .attr("width", nodeWidth)
      .attr("height", nodeHeight)
      .attr("rx", 12)
      .attr("ry", 12)
      .style("fill", "white")
      .style("stroke", (d: any) => {
        // Different border colors based on state
        if (d.data.user.isActive) {
          return (d.data.hasLeftChild || d.data.hasRightChild) && !expandedNodes.has(d.data.user.id)
            ? "#3b82f6" // Blue border for collapsed nodes with children
            : "#10b981"; // Green border for active nodes
        }
        return "#6b7280"; // Gray border for inactive nodes
      })
      .style("stroke-width", (d: any) => {
        // Thicker border for nodes with collapsed children
        return (d.data.hasLeftChild || d.data.hasRightChild) && !expandedNodes.has(d.data.user.id) ? 3 : 2;
      })
      .style("filter", "drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))");

    // Add avatar circle
    nodeEnter
      .append("circle")
      .attr("cx", nodeWidth / 2)
      .attr("cy", 25)
      .attr("r", 18)
      .style("fill", (d: any) => d.data.user.isActive ? "#10b981" : "#6b7280");

    // Add avatar text (initials)
    nodeEnter
      .append("text")
      .attr("x", nodeWidth / 2)
      .attr("y", 25)
      .attr("dy", "0.35em")
      .style("text-anchor", "middle")
      .style("fill", "white")
      .style("font-weight", "bold")
      .style("font-size", "12px")
      .text((d: any) => {
        const firstName = d.data.user.firstName || '';
        const lastName = d.data.user.lastName || '';
        return `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase();
      });

    // Add name text
    nodeEnter
      .append("text")
      .attr("x", nodeWidth / 2)
      .attr("y", 55)
      .style("text-anchor", "middle")
      .style("font-weight", "600")
      .style("font-size", "13px")
      .style("fill", "#1f2937")
      .text((d: any) => `${d.data.user.firstName} ${d.data.user.lastName}`);

    // Add status badge
    nodeEnter
      .append("rect")
      .attr("x", nodeWidth / 2 - 25)
      .attr("y", 65)
      .attr("width", 50)
      .attr("height", 16)
      .attr("rx", 8)
      .style("fill", (d: any) => d.data.user.isActive ? "#dcfce7" : "#f3f4f6")
      .style("stroke", (d: any) => d.data.user.isActive ? "#16a34a" : "#6b7280")
      .style("stroke-width", 1);

    nodeEnter
      .append("text")
      .attr("x", nodeWidth / 2)
      .attr("y", 73)
      .attr("dy", "0.35em")
      .style("text-anchor", "middle")
      .style("font-size", "10px")
      .style("font-weight", "500")
      .style("fill", (d: any) => d.data.user.isActive ? "#16a34a" : "#6b7280")
      .text((d: any) => d.data.user.isActive ? "Active" : "Inactive");

    // Add sponsor info if available
    nodeEnter
      .filter((d: any) => d.data.sponsorInfo)
      .append("text")
      .attr("x", nodeWidth / 2)
      .attr("y", 90)
      .style("text-anchor", "middle")
      .style("font-size", "9px")
      .style("fill", "#3b82f6")
      .text((d: any) => `Sponsor: ${d.data.sponsorInfo.firstName} ${d.data.sponsorInfo.lastName}`);

    // Add team counts
    nodeEnter
      .append("text")
      .attr("x", nodeWidth / 2)
      .attr("y", 105)
      .style("text-anchor", "middle")
      .style("font-size", "10px")
      .style("font-weight", "500")
      .style("fill", "#4b5563")
      .text((d: any) => `Team: ${d.data.teamCounts.total} (L:${d.data.teamCounts.left} R:${d.data.teamCounts.right})`);

    // Add expand/collapse functionality for nodes with children
    const expandableNodes = nodeEnter.filter((d: any) => d.data.hasLeftChild || d.data.hasRightChild);

    // Add expand/collapse button background
    expandableNodes
      .append("circle")
      .attr("cx", nodeWidth - 15)
      .attr("cy", 15)
      .attr("r", 10)
      .style("fill", (d: any) => expandedNodes.has(d.data.user.id) ? "#ef4444" : "#3b82f6")
      .style("stroke", "white")
      .style("stroke-width", 2)
      .style("cursor", "pointer")
      .style("transition", "all 0.2s ease")
      .style("transform-origin", "center")
      .on("click", function(event, d: any) {
        event.stopPropagation();
        handleNodeToggle(d.data.user.id);
      })
;

    // Add expand/collapse button text
    expandableNodes
      .append("text")
      .attr("x", nodeWidth - 15)
      .attr("y", 15)
      .attr("dy", "0.35em")
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("fill", "white")
      .style("cursor", "pointer")
      .style("pointer-events", "none") // Let clicks pass through to circle
      .text((d: any) => expandedNodes.has(d.data.user.id) ? "−" : "+");

    // Add zoom controls
    const controls = container.append("g")
      .attr("class", "zoom-controls")
      .attr("transform", `translate(${width + margin.left - 100}, 20)`);

    // Zoom in button
    const zoomInBtn = controls.append("g")
      .style("cursor", "pointer")
      .on("click", () => {
        svg.transition().duration(300).call(
          zoomBehavior.scaleBy, 1.5
        );
      });

    zoomInBtn.append("rect")
      .attr("width", 30)
      .attr("height", 30)
      .attr("rx", 4)
      .style("fill", "white")
      .style("stroke", "#d1d5db")
      .style("stroke-width", 1);

    zoomInBtn.append("text")
      .attr("x", 15)
      .attr("y", 15)
      .attr("dy", "0.35em")
      .style("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .text("+");

    // Zoom out button
    const zoomOutBtn = controls.append("g")
      .attr("transform", "translate(0, 35)")
      .style("cursor", "pointer")
      .on("click", () => {
        svg.transition().duration(300).call(
          zoomBehavior.scaleBy, 0.67
        );
      });

    zoomOutBtn.append("rect")
      .attr("width", 30)
      .attr("height", 30)
      .attr("rx", 4)
      .style("fill", "white")
      .style("stroke", "#d1d5db")
      .style("stroke-width", 1);

    zoomOutBtn.append("text")
      .attr("x", 15)
      .attr("y", 15)
      .attr("dy", "0.35em")
      .style("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .text("−");

    // Reset zoom button
    const resetBtn = controls.append("g")
      .attr("transform", "translate(0, 70)")
      .style("cursor", "pointer")
      .on("click", () => {
        svg.transition().duration(500).call(
          zoomBehavior.transform,
          zoomIdentity
        );
      });

    resetBtn.append("rect")
      .attr("width", 30)
      .attr("height", 30)
      .attr("rx", 4)
      .style("fill", "white")
      .style("stroke", "#d1d5db")
      .style("stroke-width", 1);

    resetBtn.append("text")
      .attr("x", 15)
      .attr("y", 15)
      .attr("dy", "0.35em")
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .text("⌂");

    } catch (error) {
      console.error('Error rendering D3 tree:', error);
    }
  }, [treeData, expandedNodes, showInactive, convertToD3Hierarchy]);

  const handleNodeToggle = useCallback((nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  const handleCopyLink = async (link: string) => {
    try {
      await copyToClipboard(link);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  useEffect(() => {
    fetchTreeData();
  }, [expandedNodes]);

  useEffect(() => {
    if (treeData) {
      renderTree();
    }
  }, [treeData, renderTree, expandedNodes]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-400" />
      </div>
    );
  }

  if (!treeData) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Tree Data</h3>
        <p className="text-gray-500">Unable to load your binary tree structure.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Referral Links */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Share2 className="h-5 w-5 text-blue-500" />
            <span>Referral Links</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700">General Referral Link</label>
              <div className="flex mt-1">
                <input
                  type="text"
                  value={treeData.referralLinks.general}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                />
                <Button
                  size="sm"
                  onClick={() => handleCopyLink(treeData.referralLinks.general)}
                  className="rounded-l-none"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">Left Side Link</label>
              <div className="flex mt-1">
                <input
                  type="text"
                  value={treeData.referralLinks.left}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                />
                <Button
                  size="sm"
                  onClick={() => handleCopyLink(treeData.referralLinks.left)}
                  className="rounded-l-none"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">Right Side Link</label>
              <div className="flex mt-1">
                <input
                  type="text"
                  value={treeData.referralLinks.right}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                />
                <Button
                  size="sm"
                  onClick={() => handleCopyLink(treeData.referralLinks.right)}
                  className="rounded-l-none"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <div className="mt-4 space-y-3">
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-xs text-blue-700">
                <strong>Enhanced Placement:</strong> New users are automatically placed in your weaker leg
                for optimal network balance. Use specific side links to target placement.
              </p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <p className="text-xs text-green-700">
                <strong>Binary Matching:</strong> Points are matched weekly on Saturdays at 15:00 UTC.
                Each $100 investment by your downline = 1 binary point. Maximum 100 points per side.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Direct Referrals</p>
                <p className="text-2xl font-bold">{treeData.statistics.totalDirectReferrals}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Left Side</p>
                <p className="text-lg font-bold text-green-600">{treeData.statistics.leftReferrals || 0} users</p>
                <p className="text-sm text-green-500">{treeData.statistics.binaryPoints.leftPoints} points</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Right Side</p>
                <p className="text-lg font-bold text-orange-600">{treeData.statistics.rightReferrals || 0} users</p>
                <p className="text-sm text-orange-500">{treeData.statistics.binaryPoints.rightPoints} points</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Binary Points</p>
                <p className="text-lg font-bold text-purple-600">{treeData.statistics.binaryPoints.matchedPoints} matched</p>
                <p className="text-sm text-purple-500">
                  {Math.min(treeData.statistics.binaryPoints.leftPoints, treeData.statistics.binaryPoints.rightPoints)} available
                </p>
              </div>
              <Award className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tree Visualization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Binary Tree Structure</span>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowInactive(!showInactive)}
              >
                {showInactive ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showInactive ? 'Hide Inactive' : 'Show Inactive'}
              </Button>
              <Button size="sm" onClick={fetchTreeData}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border border-gray-200 rounded-lg overflow-hidden bg-gradient-to-br from-slate-50 to-blue-50">
            <svg
              ref={svgRef}
              className="w-full"
              style={{ height: '700px' }}
            />
          </div>
          <div className="mt-4 text-sm text-gray-600 space-y-1">
            <p>• <strong>Zoom:</strong> Use mouse wheel or zoom controls</p>
            <p>• <strong>Pan:</strong> Click and drag to move around</p>
            <p>• <strong>Expand/Collapse:</strong> Click the + or - button on nodes</p>
          </div>
        </CardContent>
      </Card>



      {/* Binary Points Information Panel */}
      <BinaryPointsInfoPanel />
    </div>
  );
};
